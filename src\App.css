
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Fix for roadmap text wrapping issues */
#roadmap .hover-card p {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  text-rendering: optimizeLegibility !important;
  text-decoration: none !important;
  text-transform: none !important;
  text-indent: 0 !important;
  text-align: left !important;
}

/* Ensure consistent status label alignment */
.roadmap-status {
  display: flex;
  align-items: center;
  height: 1.5rem;
  min-height: 1.5rem;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.roadmap-status span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1;
}

/* Navigation item hover animation */
.nav-icon {
  display: inline-flex;
  margin-right: 0.25rem;
  vertical-align: middle;
  transition: transform 0.3s ease;
}

/* Custom utility for rotating icons on hover */
.group:hover .nav-icon {
  transform: rotate(15deg) scale(1.05);
}

/* 3D cube text rotation utilities */
.perspective-\\[1000px\\] {
  perspective: 1000px;
}

/* Added z-index to ensure rotation is visible */
.hero-section h1 {
  position: relative;
  z-index: 10;
}
