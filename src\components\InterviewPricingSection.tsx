import { But<PERSON> } from "@/components/ui/button";
import { Check, Star } from "lucide-react";
import { useState } from "react";
import { EmailFormDialog } from "@/components/EmailFormDialog";

const InterviewPricingSection = () => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const plans = [
    {
      name: "Starter",
      price: "19",
      period: "month",
      description: "Perfect for getting started with interview prep",
      features: [
        "5 AI mock interviews per month",
        "Basic performance analytics",
        "General interview questions",
        "Email support",
        "Mobile app access"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: "49",
      period: "month",
      description: "Best for serious job seekers and career advancement",
      features: [
        "Unlimited AI mock interviews",
        "Advanced performance analytics",
        "Role-specific question banks",
        "Real-time feedback & coaching",
        "Priority support",
        "Resume review assistance",
        "Custom interview scenarios"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "99",
      period: "month",
      description: "For teams and organizations training multiple candidates",
      features: [
        "Everything in Professional",
        "Team management dashboard",
        "Bulk user management",
        "Custom branding",
        "API access",
        "Dedicated account manager",
        "Advanced reporting & insights"
      ],
      popular: false
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-gloria-dark">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gloria-light">
            Choose Your Training Plan
          </h2>
          <p className="text-gloria-light text-lg max-w-3xl mx-auto leading-relaxed">
            Start with our free trial, then select the plan that fits your interview preparation needs.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <div 
              key={index}
              className={`bg-gloria-dark-accent border rounded-xl p-8 text-center hover:shadow-lg hover:shadow-gloria-primary/10 hover-card transition-all ${
                plan.popular 
                  ? 'border-gloria-primary/50 relative scale-105' 
                  : 'border-gloria-primary/20 hover:border-gloria-primary/50'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gloria-primary text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-1">
                    <Star className="h-4 w-4" />
                    Most Popular
                  </div>
                </div>
              )}
              
              <h3 className="text-2xl font-bold mb-2 text-white">
                {plan.name}
              </h3>
              
              <p className="text-gloria-gray mb-6">
                {plan.description}
              </p>
              
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">${plan.price}</span>
                <span className="text-gloria-gray">/{plan.period}</span>
              </div>
              
              <ul className="space-y-3 mb-8 text-left">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <Check className="h-5 w-5 text-gloria-primary mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-white">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <Button 
                className={`w-full py-3 ${
                  plan.popular 
                    ? 'bg-gloria-primary text-white hover:bg-gloria-primary/90' 
                    : 'bg-transparent border border-gloria-primary text-gloria-primary hover:bg-gloria-primary hover:text-white'
                }`}
                onClick={() => setDialogOpen(true)}
              >
                {plan.name === 'Enterprise' ? 'Contact Sales' : 'Start Free Trial'}
              </Button>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-gloria-gray mb-4">
            All plans include a 7-day free trial. No credit card required.
          </p>
          <p className="text-gloria-gray">
            Need a custom solution? <button className="text-gloria-primary hover:underline" onClick={() => setDialogOpen(true)}>Contact our team</button>
          </p>
        </div>
      </div>
      
      <EmailFormDialog open={dialogOpen} onOpenChange={setDialogOpen} />
    </section>
  );
};

export default InterviewPricingSection;