import { AlertTrian<PERSON>, Clock, TrendingDown } from "lucide-react";

const InterviewProblemSection = () => {
  const problems = [
    {
      icon: <AlertTriangle className="h-8 w-8 text-gloria-secondary" />,
      title: "Interview Anxiety",
      description: "70% of candidates struggle with nerves and lack confidence during interviews, leading to poor performance despite strong qualifications."
    },
    {
      icon: <Clock className="h-8 w-8 text-gloria-secondary" />,
      title: "Limited Practice Opportunities",
      description: "Most candidates only practice right before interviews, missing crucial preparation time and feedback to improve their responses."
    },
    {
      icon: <TrendingDown className="h-8 w-8 text-gloria-secondary" />,
      title: "Generic Preparation",
      description: "One-size-fits-all interview prep doesn't address specific role requirements, company culture, or individual improvement areas."
    }
  ];

  return (
    <section className="py-12 md:py-16 bg-gloria-dark border-t border-gloria-primary/10">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gloria-light">
            Why Most Interviews Fail
          </h2>
          <p className="text-gloria-light text-lg max-w-3xl mx-auto leading-relaxed">
            Despite having strong skills, talented candidates often struggle in interviews due to 
            inadequate preparation, anxiety, and lack of personalized feedback.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {problems.map((problem, index) => (
            <div 
              key={index}
              className="bg-gloria-dark-accent border border-gloria-primary/20 rounded-xl p-6 text-center hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card"
            >
              <div className="flex justify-center mb-4">
                {problem.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-white">
                {problem.title}
              </h3>
              <p className="text-white leading-relaxed">
                {problem.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default InterviewProblemSection;