import { Database, Shield, Users, Award } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
const CombinedFeatureSection = () => {
  const differentiators = [{
    icon: <Database className="h-12 w-12 text-gloria-secondary" />,
    title: "High-Quality, Real-Time Curated Data",
    description: "Multiple verified streams across media, markets, and onchain activity with graph-based contextual relationships ensure comprehensive, real-time information."
  }, {
    icon: <Shield className="h-12 w-12 text-gloria-secondary" />,
    title: "Customizable Subfilters",
    description: "Domain presets, entity weighting, and personalized filters tailored to your specific AI agent requirements."
  }, {
    icon: <Users className="h-12 w-12 text-gloria-secondary" />,
    title: "Multi-Agent Architecture",
    description: "Our collaborative AI system ensures minimal hallucinations and maximum reliability for critical applications."
  }, {
    icon: <Award className="h-12 w-12 text-gloria-secondary" />,
    title: "Deep Industry Expertise",
    description: "Our agent Gloria on X is a live example of real-time, AI-driven news curation—showing how our platform delivers unbiased, context-rich updates."
  }];
  return <section id="features" className="py-6 md:py-8 bg-gloria-dark/95">
      <div className="container mx-auto">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mt-8 mb-10 text-center text-gloria-light">Why Gloria AI Stands Out</h2>
          
          <p className="text-gloria-light text-lg text-center mb-16 max-w-3xl mx-auto leading-relaxed">
            Gloria AI offers unparalleled advantages for developers and organizations looking to power their AI systems with high-quality, contextual data and market intelligence.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-20 max-w-[900px] mx-auto">
            {differentiators.map((item, index) => <div key={index} className="bg-gloria-dark-accent border border-gloria-primary/20 rounded-xl p-8 flex flex-col items-center text-center h-full hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card">
                <div className="mb-4">{item.icon}</div>
                <h3 className="text-xl font-semibold mb-4" style={{
              color: "#FFFFFF"
            }}>{item.title}</h3>
                {item.title === "Deep Industry Expertise" ? <p className="leading-relaxed text-base text-zinc-50">{item.description}</p> : <p className="text-base leading-relaxed text-zinc-50">{item.description}</p>}
              </div>)}
          </div>
          
          <div className="mt-8 mb-16 max-w-[900px] mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold mb-10 text-center text-gloria-light">How It All Works Together</h3>
            
            <p className="text-gloria-light text-lg text-center mb-16 max-w-3xl mx-auto leading-relaxed">
              Gloria AI ingests data from multiple sources, enriches it with a dynamic knowledge graph, and delivers curated, real-time data feeds.
            </p>
            
            <div className="bg-gloria-dark-accent border border-gloria-primary/20 rounded-xl p-8 hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card">
              <div className="flex flex-col md:flex-row items-center justify-between text-center md:text-left gap-4">
                <div className="flex-1">
                  <div className="bg-gloria-dark p-6 rounded-lg border border-gloria-primary/20 mb-3 hover:border-gloria-primary/40 transition-all duration-300">
                    <h4 className="font-medium text-white text-lg mb-2">Data Sources</h4>
                    <p className="text-white">Multiple verified streams across media, markets, and onchain activity</p>
                  </div>
                </div>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right hidden md:block text-gloria-primary">
                  <path d="M5 12h14" />
                  <path d="m12 5 7 7-7 7" />
                </svg>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-down md:hidden text-gloria-primary">
                  <path d="M12 5v14" />
                  <path d="m19 12-7 7-7-7" />
                </svg>
                
                <div className="flex-1">
                  <div className="bg-gloria-dark p-6 rounded-lg border border-gloria-primary/20 mb-3 hover:border-gloria-primary/40 transition-all duration-300">
                    <h4 className="font-medium text-white text-lg mb-2">Knowledge Graph</h4>
                    <p className="text-white">Contextual relationships and signal extraction</p>
                  </div>
                </div>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right hidden md:block text-gloria-primary">
                  <path d="M5 12h14" />
                  <path d="m12 5 7 7-7 7" />
                </svg>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-down md:hidden text-gloria-primary">
                  <path d="M12 5v14" />
                  <path d="m19 12-7 7-7-7" />
                </svg>
                
                <div className="flex-1">
                  <div className="bg-gloria-dark p-6 rounded-lg border border-gloria-primary/20 mb-3 hover:border-gloria-primary/40 transition-all duration-300">
                    <h4 className="font-medium text-white text-lg mb-2">Curated Feeds</h4>
                    <p className="text-white">Structured outputs optimized for AI agents</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default CombinedFeatureSection;