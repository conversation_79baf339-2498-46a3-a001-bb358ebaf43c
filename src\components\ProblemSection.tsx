
import { AlertTriangle, Clock, TrendingDown } from "lucide-react";

const ProblemSection = () => {
  const problems = [
    {
      icon: <AlertTriangle className="h-8 w-8 text-gloria-secondary" />,
      title: "Information Overload",
      description: "AI agents struggle to filter relevant data from the massive volume of information available across different sources."
    },
    {
      icon: <Clock className="h-8 w-8 text-gloria-secondary" />,
      title: "Delayed Insights",
      description: "Critical market movements and news often get processed too late, missing valuable opportunities for timely decision-making."
    },
    {
      icon: <TrendingDown className="h-8 w-8 text-gloria-secondary" />,
      title: "Context-Poor Data",
      description: "Raw data feeds lack the contextual relationships and signal extraction needed for AI agents to make informed decisions."
    }
  ];

  return (
    <section className="py-12 md:py-16 bg-gloria-dark border-t border-gloria-primary/10">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gloria-light">
            Cut Through the Noise
          </h2>
          <p className="text-gloria-light text-lg max-w-3xl mx-auto leading-relaxed">
            AI agents and developers face critical challenges when trying to access high-quality, 
            real-time data for decision-making and automation.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {problems.map((problem, index) => (
            <div 
              key={index}
              className="bg-gloria-dark-accent border border-gloria-primary/20 rounded-xl p-6 text-center hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card"
            >
              <div className="flex justify-center mb-4">
                {problem.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-white">
                {problem.title}
              </h3>
              <p className="text-white leading-relaxed">
                {problem.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProblemSection;
