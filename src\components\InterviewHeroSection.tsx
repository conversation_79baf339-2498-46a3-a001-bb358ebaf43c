import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { EmailFormDialog } from "@/components/EmailFormDialog";
import { useIsMobile } from "@/hooks/use-mobile";
import { Play, Users, Award } from "lucide-react";

const InterviewHeroSection = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const rotatingWords = ["Technical", "Behavioral", "Leadership"];
  const [index, setIndex] = useState(0);
  const isMobile = useIsMobile();

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex(prev => (prev + 1) % rotatingWords.length);
    }, 2500);

    return () => clearInterval(interval);
  }, []);

  const paragraphText = isMobile 
    ? "Master any interview with AI-powered practice sessions, real-time feedback, and personalized coaching." 
    : "Transform your interview performance with our AI-powered training platform. Get real-time feedback, practice with industry-specific scenarios, and build confidence through personalized coaching sessions.";

  return (
    <section id="hero" className="min-h-[80vh] flex items-center hero-section overflow-x-hidden">
      <div className="container mx-auto px-4 sm:px-6 md:px-8 max-w-full">
        <div className="max-w-4xl mx-auto text-center flex flex-col items-center animate-fade-in pt-2 md:pt-2 lg:pt-2">
          
          <h1 className="text-4xl md:text-[3.5rem] lg:text-7xl font-bold mb-4 text-gloria-light tracking-tight leading-tight text-center font-manrope font-[400] locked-font-weight" style={{
            letterSpacing: "-0.01em",
            width: "100%",
            margin: "0 auto",
            marginBottom: "0"
          }}>
            <span style={{
              display: "block",
              textAlign: "center"
            }} className="inline-block whitespace-nowrap text-[1em] text-center w-full font-normal md:text-7xl">
              Ace Every
            </span>
            
            <div className="relative h-[4rem] md:h-[6rem] lg:h-[7rem] overflow-visible mt-8 md:mt-16 mx-auto min-w-[10rem] md:min-w-[12rem]" style={{
              width: isMobile ? "100%" : "min-content",
              perspective: "1000px"
            }}>
              <div className="transition-transform duration-[1500ms] ease-in-out" style={{
                transformStyle: "preserve-3d",
                transform: `rotateX(${index * 120}deg)`,
                transitionDuration: "1500ms"
              }}>
                {rotatingWords.map((word, i) => (
                  <div key={i} className="absolute top-0 left-0 w-full h-full flex items-center justify-center text-white whitespace-nowrap text-4xl md:text-[3.5rem] lg:text-7xl text-center" style={{
                    transform: `rotateX(-${i * 120}deg) translateZ(${isMobile ? "2.5rem" : "4rem"})`,
                    backfaceVisibility: "hidden",
                    opacity: 1,
                    backgroundColor: "#000000",
                    padding: "0 10px"
                  }}>
                    {word} Interview
                  </div>
                ))}
              </div>
            </div>
          </h1>
          
          <p className="text-[#F3F3F3] text-lg mb-8 font-manrope mx-auto text-center max-w-[825px]" style={{
            marginTop: "0"
          }}>
            {paragraphText}
          </p>

          {/* Stats Section */}
          <div className="grid grid-cols-3 gap-8 mb-12 max-w-lg mx-auto">
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Users className="h-8 w-8 text-gloria-primary" />
              </div>
              <div className="text-2xl font-bold text-white">10K+</div>
              <div className="text-sm text-gloria-gray">Users Trained</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Award className="h-8 w-8 text-gloria-primary" />
              </div>
              <div className="text-2xl font-bold text-white">95%</div>
              <div className="text-sm text-gloria-gray">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-2">
                <Play className="h-8 w-8 text-gloria-primary" />
              </div>
              <div className="text-2xl font-bold text-white">50K+</div>
              <div className="text-sm text-gloria-gray">Practice Sessions</div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-16 max-w-xs sm:max-w-lg mx-auto px-4">
            <Button 
              size="lg" 
              onClick={() => setDialogOpen(true)} 
              className="bg-gloria-primary text-white hover:bg-gloria-primary/90 px-4 text-sm font-manrope py-[13px] rounded-3xl"
            >
              Start Free Trial
            </Button>
            <Button 
              variant="outline" 
              className="border-white text-white bg-transparent hover:bg-white/10 py-3 px-4 text-sm font-manrope w-full" 
              size="lg"
              onClick={() => setDialogOpen(true)}
            >
              Watch Demo
            </Button>
          </div>
        </div>
      </div>

      <EmailFormDialog open={dialogOpen} onOpenChange={setDialogOpen} />
    </section>
  );
};

export default InterviewHeroSection;