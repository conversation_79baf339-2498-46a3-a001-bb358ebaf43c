@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 100%;

    --card: 222 15% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 217 100% 44%;
    --primary-foreground: 0 0% 100%;

    --secondary: 219 31% 62%;
    --secondary-foreground: 0 0% 100%;

    --muted: 222 15% 14%;
    --muted-foreground: 215 8% 70%;

    --accent: 217 100% 44%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 222 15% 18%;
    --input: 222 15% 18%;
    --ring: 217 100% 44%;

    --radius: 0.5rem;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Manrope', sans-serif;
    font-size: 16px;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-gloria-light font-bold font-manrope;
    margin-top: 1.5em;
    margin-bottom: 0.75em;
  }

  /* CRITICAL: DO NOT MODIFY - Font weight for specific headings that need to match */
  .locked-font-weight {
    font-weight: 400 !important; /* Using !important to ensure it can't be overridden */
  }

  /* Card and feature titles */
  .feature-card h3, 
  .hover-card h3 {
    @apply text-gloria-primary font-manrope;
  }
  
  /* Ensure "How It All Works Together" is white */
  .hover-card h3.text-center {
    @apply text-white font-manrope;
  }
  
  /* Ensure roadmap titles are white */
  #roadmap h3 {
    @apply text-white font-manrope;
  }

  p {
    @apply font-manrope;
    margin-bottom: 1.25rem;
  }

  .section-padding {
    @apply py-20 md:py-28;
  }

  .hero-section {
    @apply bg-gloria-dark;
  }

  .feature-card {
    background: rgba(23, 27, 38, 0.95);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
  }
  
  .feature-card:hover {
    box-shadow: 0 0 20px rgba(0, 99, 226, 0.15);
    transform: translateY(-3px);
  }
  
  .cyber-border {
    position: relative;
    border: 1px solid rgba(0, 99, 226, 0.2);
    overflow: hidden;
  }
  
  .cyber-border::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 1px;
    background: rgba(0, 99, 226, 0.6);
    animation: cyber-border-slide 3s infinite;
  }
  
  @keyframes cyber-border-slide {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  .neon-text {
    text-shadow: 0 0 10px rgba(0, 99, 226, 0.7);
  }

  /* Section divider style */
  .section-divider {
    @apply border-t border-gloria-primary/10 my-8 md:my-12;
  }
  
  /* Improved button styles */
  .gloria-button {
    @apply bg-gloria-primary text-gloria-light font-manrope font-medium py-3 px-6 rounded-full transition-all duration-300;
  }
  
  .gloria-button:hover {
    @apply bg-gloria-primary/90 shadow-md shadow-gloria-primary/20;
  }

  /* Card hover effects */
  .hover-card {
    @apply transition-all duration-300;
  }
  
  .hover-card:hover {
    @apply transform -translate-y-1 shadow-lg shadow-gloria-primary/10;
  }

  /* Add font classes to buttons */
  button {
    @apply font-manrope;
  }

  /* Add font classes to navbar links */
  nav a {
    @apply font-manrope;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gloria-dark text-foreground;
  }

  .container {
    @apply px-6 md:px-8 lg:px-10;
  }
}
