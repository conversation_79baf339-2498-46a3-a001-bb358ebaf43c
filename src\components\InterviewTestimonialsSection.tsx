import { Star } from "lucide-react";

const InterviewTestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Software Engineer at Meta",
      content: "The AI feedback was incredibly detailed and helped me identify specific areas to improve. I went from failing technical interviews to landing my dream job at Meta!",
      rating: 5,
      avatar: "/lovable-uploads/579f3c90-5010-4a15-889b-3ce7bde9575e.png"
    },
    {
      name: "<PERSON>",
      role: "Product Manager at Google",
      content: "The behavioral interview practice was game-changing. The AI asked follow-up questions just like real interviewers and helped me craft compelling stories.",
      rating: 5,
      avatar: "/lovable-uploads/579f3c90-5010-4a15-889b-3ce7bde9575e.png"
    },
    {
      name: "<PERSON>",
      role: "Data Scientist at Netflix",
      content: "I loved how the system adapted to my progress. The role-specific questions for data science positions were spot-on and prepared me perfectly.",
      rating: 5,
      avatar: "/lovable-uploads/579f3c90-5010-4a15-889b-3ce7bde9575e.png"
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-gloria-dark-accent">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gloria-light">
            Success Stories
          </h2>
          <p className="text-gloria-light text-lg max-w-3xl mx-auto leading-relaxed">
            Join thousands of professionals who've transformed their interview performance and landed their dream jobs.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-gloria-dark border border-gloria-primary/20 rounded-xl p-6 hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card"
            >
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              
              <p className="text-white leading-relaxed mb-6 italic">
                "{testimonial.content}"
              </p>
              
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gloria-primary rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-semibold text-lg">
                    {testimonial.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="text-white font-semibold">
                    {testimonial.name}
                  </div>
                  <div className="text-gloria-gray text-sm">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default InterviewTestimonialsSection;