import { Brain, MessageCircle, Bar<PERSON>hart3, Target } from "lucide-react";

const InterviewFeatureSection = () => {
  const features = [
    {
      icon: <Brain className="h-12 w-12 text-gloria-secondary" />,
      title: "AI-Powered Mock Interviews",
      description: "Practice with our advanced AI interviewer that adapts to your responses and provides realistic interview scenarios across all industries."
    },
    {
      icon: <MessageCircle className="h-12 w-12 text-gloria-secondary" />,
      title: "Real-Time Feedback",
      description: "Get instant analysis of your responses, body language, speech patterns, and confidence levels with actionable improvement suggestions."
    },
    {
      icon: <BarChart3 className="h-12 w-12 text-gloria-secondary" />,
      title: "Performance Analytics",
      description: "Track your progress over time with detailed analytics on response quality, speaking pace, filler words, and overall interview performance."
    },
    {
      icon: <Target className="h-12 w-12 text-gloria-secondary" />,
      title: "Role-Specific Training",
      description: "Customize your practice sessions based on specific job roles, companies, and industries with curated question banks and scenarios."
    }
  ];

  return (
    <section id="features" className="py-6 md:py-8 bg-gloria-dark/95">
      <div className="container mx-auto">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mt-8 mb-10 text-center text-gloria-light">
            Master Interviews with AI Training
          </h2>
          
          <p className="text-gloria-light text-lg text-center mb-16 max-w-3xl mx-auto leading-relaxed">
            Our AI-powered platform provides personalized interview training that adapts to your needs, 
            helping you build confidence and improve performance through realistic practice sessions.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-20 max-w-[900px] mx-auto">
            {features.map((item, index) => (
              <div 
                key={index} 
                className="bg-gloria-dark-accent border border-gloria-primary/20 rounded-xl p-8 flex flex-col items-center text-center h-full hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card"
              >
                <div className="mb-4">{item.icon}</div>
                <h3 className="text-xl font-semibold mb-4 text-white">
                  {item.title}
                </h3>
                <p className="text-base leading-relaxed text-zinc-50">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
          
          <div className="mt-8 mb-16 max-w-[900px] mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold mb-10 text-center text-gloria-light">
              How Our AI Training Works
            </h3>
            
            <p className="text-gloria-light text-lg text-center mb-16 max-w-3xl mx-auto leading-relaxed">
              Our intelligent system analyzes your responses, provides instant feedback, and creates personalized training plans to maximize your interview success.
            </p>
            
            <div className="bg-gloria-dark-accent border border-gloria-primary/20 rounded-xl p-8 hover:border-gloria-primary/50 transition-all hover:shadow-lg hover:shadow-gloria-primary/10 hover-card">
              <div className="flex flex-col md:flex-row items-center justify-between text-center md:text-left gap-4">
                <div className="flex-1">
                  <div className="bg-gloria-dark p-6 rounded-lg border border-gloria-primary/20 mb-3 hover:border-gloria-primary/40 transition-all duration-300">
                    <h4 className="font-medium text-white text-lg mb-2">AI Assessment</h4>
                    <p className="text-white">Analyze your current interview skills and identify improvement areas</p>
                  </div>
                </div>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right hidden md:block text-gloria-primary">
                  <path d="M5 12h14" />
                  <path d="m12 5 7 7-7 7" />
                </svg>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-down md:hidden text-gloria-primary">
                  <path d="M12 5v14" />
                  <path d="m19 12-7 7-7-7" />
                </svg>
                
                <div className="flex-1">
                  <div className="bg-gloria-dark p-6 rounded-lg border border-gloria-primary/20 mb-3 hover:border-gloria-primary/40 transition-all duration-300">
                    <h4 className="font-medium text-white text-lg mb-2">Personalized Training</h4>
                    <p className="text-white">Practice with customized scenarios and receive real-time coaching</p>
                  </div>
                </div>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right hidden md:block text-gloria-primary">
                  <path d="M5 12h14" />
                  <path d="m12 5 7 7-7 7" />
                </svg>
                
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-down md:hidden text-gloria-primary">
                  <path d="M12 5v14" />
                  <path d="m19 12-7 7-7-7" />
                </svg>
                
                <div className="flex-1">
                  <div className="bg-gloria-dark p-6 rounded-lg border border-gloria-primary/20 mb-3 hover:border-gloria-primary/40 transition-all duration-300">
                    <h4 className="font-medium text-white text-lg mb-2">Interview Success</h4>
                    <p className="text-white">Confidently ace your interviews with improved skills and preparation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InterviewFeatureSection;